#!/bin/bash

# 构建和运行 Resume Admin Docker 镜像的脚本

# 设置变量
IMAGE_NAME="resume-admin"
TAG="latest"
CONTAINER_NAME="resume-admin-container"
PORT="3000"

echo "🚀 开始构建 Resume Admin Docker 镜像..."

# 构建镜像
docker build -t ${IMAGE_NAME}:${TAG} .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功！"
    
    # 停止并删除已存在的容器
    echo "🔄 停止并删除已存在的容器..."
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    
    # 运行新容器
    echo "🎯 启动新容器..."
    docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${PORT}:80 \
        --restart unless-stopped \
        ${IMAGE_NAME}:${TAG}
    
    if [ $? -eq 0 ]; then
        echo "🎉 容器启动成功！"
        echo "📱 访问地址: http://localhost:${PORT}"
        echo "🔍 查看日志: docker logs ${CONTAINER_NAME}"
        echo "⏹️  停止容器: docker stop ${CONTAINER_NAME}"
    else
        echo "❌ 容器启动失败！"
        exit 1
    fi
else
    echo "❌ 镜像构建失败！"
    exit 1
fi
