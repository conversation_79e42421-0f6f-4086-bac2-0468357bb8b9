import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';

// 懒加载组件
const Login = lazy(() => import('./views/Login'));
const Dashboard = lazy(() => import('./views/Dashboard'));
const NotFound = lazy(() => import('./views/NotFound'));
const AdminLayout = lazy(() => import('./components/AdminLayout'));

// 加载组件包装器
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense 
    fallback={
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    }
  >
    {children}
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/backend/login" replace />,
  },
  {
    path: '/backend/login',
    element: (
      <LazyWrapper>
        <Login />
      </LazyWrapper>
    ),
  },
  {
    path: '/backend',
    element: (
      <LazyWrapper>
        <AdminLayout />
      </LazyWrapper>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/backend/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: (
          <LazyWrapper>
            <Dashboard />
          </LazyWrapper>
        ),
      },
      // 后续可以添加更多页面
      // {
      //   path: 'users',
      //   element: <LazyWrapper><UserManagement /></LazyWrapper>,
      // },
      // {
      //   path: 'resumes',
      //   element: <LazyWrapper><ResumeManagement /></LazyWrapper>,
      // },
    ],
  },
  // 404 页面
  {
    path: '*',
    element: (
      <LazyWrapper>
        <NotFound />
      </LazyWrapper>
    ),
  },
]);
