import axios, { type AxiosResponse } from 'axios';
import { message } from 'antd';
import { handle401Error } from './auth';

// 后端返回的数据结构
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8082/admin',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加 token - 优先使用管理员 token
    const adminToken = localStorage.getItem('admin_token');
    const userToken = localStorage.getItem('token');
    const token = adminToken || userToken;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log('🚀 Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      data: config.data,
      token: token ? `${token.substring(0, 10)}...` : 'none',
    });

    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('✅ Response:', response.data);
    
    // 直接返回 data 字段
    return response.data.data;
  },
  (error) => {
    console.error('❌ Response Error:', error);
    
    // 处理 HTTP 状态码错误
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录');
          // 使用统一的 401 处理函数
          handle401Error();
          break;
        case 403:
          message.error('权限不足');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          message.error('服务器内部错误');
          break;
        default:
          // 如果后端返回了错误信息结构，使用后端的 message
          if (data && data.message) {
            message.error(data.message);
          } else {
            message.error(`请求失败 (${status})`);
          }
      }
      
      // 返回后端的错误信息结构
      return Promise.reject(data || { code: status, message: '请求失败', data: null });
    } else if (error.request) {
      message.error('网络错误，请检查网络连接');
      return Promise.reject({ code: -1, message: '网络错误', data: null });
    } else {
      message.error('请求配置错误');
      return Promise.reject({ code: -1, message: '请求配置错误', data: null });
    }
  }
);

// GET 请求
export const get = <T = any>(url: string, config?: any): Promise<T> => {
  return request.get(url, config);
};

// POST 请求
export const post = <T = any>(url: string, data?: any, config?: any): Promise<T> => {
  return request.post(url, data, config);
};

// PUT 请求
export const put = <T = any>(url: string, data?: any, config?: any): Promise<T> => {
  return request.put(url, data, config);
};

// DELETE 请求
export const del = <T = any>(url: string, config?: any): Promise<T> => {
  return request.delete(url, config);
};

// 导出默认实例（如果需要更复杂的操作）
export default request;
