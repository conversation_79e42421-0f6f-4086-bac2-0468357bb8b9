import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { adminApi, authApi } from '../api';
import type { AdminInfoResponse, AdminLoginParams } from '../api/types/auth';

interface AdminState {
  // 状态
  adminInfo: AdminInfoResponse | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // 操作
  login: (credentials: AdminLoginParams) => Promise<void>;
  logout: () => void;
  getAdminInfo: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  clearAdminInfo: () => void;
}

export const useAdminStore = create<AdminState>()(
  persist(
    (set, get) => {
      // 监听 401 登出事件
      if (typeof window !== 'undefined') {
        window.addEventListener('auth:logout', async () => {
          await get().logout();
        });
      }

      return {
      // 初始状态
      adminInfo: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,
      
      // 管理员登录
      login: async (credentials) => {
        try {
          set({ isLoading: true });
          const response = await authApi.adminLogin(credentials);
          const { token } = response;
          
          // 保存 token 到 localStorage
          localStorage.setItem('admin_token', token);
          
          set({
            token,
            isAuthenticated: true,
            isLoading: false,
          });
          
          // 登录成功后获取管理员信息
          await get().getAdminInfo();
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },
      
      // 登出
      logout: async () => {
        try {
          // 调用退出登录接口
          await adminApi.logout();
        } catch (error) {
          console.error('退出登录接口调用失败:', error);
          // 即使接口调用失败，也要清除本地状态
        } finally {
          // 清除本地存储和状态
          localStorage.removeItem('admin_token');
          set({
            adminInfo: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },
      
      // 获取管理员信息
      getAdminInfo: async () => {
        try {
          set({ isLoading: true });
          const adminInfo = await adminApi.getAdminInfo();
          set({
            adminInfo,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          // 如果获取管理员信息失败，清除认证状态
          get().logout();
          throw error;
        }
      },
      
      // 设置加载状态
      setLoading: (loading) => set({ isLoading: loading }),
      
      // 清除管理员信息
      clearAdminInfo: () => set({ adminInfo: null }),
      };
    },
    {
      name: 'admin-storage', // localStorage 中的 key
      partialize: (state) => ({ 
        token: state.token,
        adminInfo: state.adminInfo,
        isAuthenticated: state.isAuthenticated,
      }), // 只持久化这些字段
    }
  )
);
