import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown } from 'antd';
import {
  DashboardOutlined,
  LogoutOutlined,
  UserOutlined,
  CreditCardOutlined,
} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import BreadcrumbHeader from '@/components/Breadcrumb';
import { useAdminStore } from '@/store/adminStore';

const { Header, Sider, Content } = Layout;

const AdminLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>(['channels']);
  const navigate = useNavigate();
  const location = useLocation();
  const { adminInfo, logout, getAdminInfo, isAuthenticated, token } = useAdminStore();

  // 组件挂载时获取用户信息
  useEffect(() => {
    // 检查 localStorage 中的 token（处理页面刷新的情况）
    const storedToken = localStorage.getItem('admin_token');

    // 如果有 token 但没有用户信息，则获取用户信息
    if ((token || storedToken) && !adminInfo) {
      console.log('正在获取管理员信息...');
      getAdminInfo().catch((error) => {
        console.error('获取管理员信息失败:', error);
        // 如果获取用户信息失败，跳转到登录页
        navigate('/backend/login');
      });
    }
    // 如果没有 token 但是在后台页面，跳转到登录页
    else if (!token && !storedToken && location.pathname.startsWith('/backend') && location.pathname !== '/backend/login') {
      console.log('未找到 token，跳转到登录页');
      navigate('/backend/login');
    }
  }, [token, adminInfo, getAdminInfo, navigate, location.pathname]);

  // 监听 401 登出事件
  useEffect(() => {
    const handleLogout = () => {
      // 确保在事件触发时跳转到登录页
      navigate('/backend/login');
    };

    window.addEventListener('auth:logout', handleLogout);

    return () => {
      window.removeEventListener('auth:logout', handleLogout);
    };
  }, [navigate]);

  // 确保页面刷新后菜单展开状态正确
  useEffect(() => {
    const pathname = location.pathname;
    if (pathname.includes('/channel-users') || pathname.includes('/channel-payment')) {
      if (!openKeys.includes('channels')) {
        setOpenKeys(['channels']);
      }
    }
  }, [location.pathname, openKeys]);

  // 菜单项配置
  const menuItems = [
    {
      key: 'channels',
      icon: <DashboardOutlined />,
      label: '渠道管理',
      children: [
        {
          key: 'channel-users',
          icon: <UserOutlined />,
          label: '渠道用户',
        },
        {
          key: 'channel-payment',
          icon: <CreditCardOutlined />,
          label: '渠道付费',
        },
      ],
    },
  ];

  // 处理退出登录
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/backend/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使退出失败，也要跳转到登录页
      navigate('/backend/login');
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(`/backend/${key}`);
  };

  // 获取当前选中的菜单项
  const getCurrentMenuKey = () => {
    const pathname = location.pathname;
    if (pathname.startsWith('/backend/')) {
      const key = pathname.replace('/backend/', '');
      // 确保选中的菜单对应的父菜单是展开的
      if (key === 'channel-users' || key === 'channel-payment') {
        if (!openKeys.includes('channels')) {
          setOpenKeys(['channels']);
        }
      }
      return key;
    }
    return 'channel-users';
  };

  // 处理菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  return (
    <Layout className="min-h-screen">
      {/* 左侧菜单 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        collapsedWidth={0}
        className="shadow-lg"
        width={240}
      >
        {/* Logo 区域 */}
        <div className="h-16 flex items-center justify-center border-b border-gray-100">
          <img
            src="/backend/logo.webp"
            alt="熊猫简历"
            className={`object-contain transition-all duration-300 ${
              collapsed ? 'w-12 h-12' : 'h-12'
            }`}
          />
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[getCurrentMenuKey()]}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-none text-base"
          style={{
            height: 'calc(100vh - 64px)',
            fontSize: '16px'
          }}
        />
      </Sider>

      <Layout>
        {/* 头部 */}
        <Header
          className="shadow-sm flex items-center justify-between h-16"
          style={{ padding: '0 16px 0 12px' }}
        >
          {/* 左侧：面包屑导航 */}
          <BreadcrumbHeader
            collapsed={collapsed}
            onToggleCollapse={() => setCollapsed(!collapsed)}
            onRefresh={() => {
              // 可以在这里添加自定义刷新逻辑
              window.location.reload();
            }}
          />

          {/* 右侧：用户头像和下拉菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow
          >
            <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1">
              <Avatar
                size="small"
                src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin"
                className="border border-gray-200"
              />
              <div className="hidden md:block">
                <div className="text-sm font-medium text-gray-800">
                  {adminInfo?.username || '管理员'}
                </div>
                <div className="text-xs text-gray-500">
                  {adminInfo?.is_super_admin ? '超级管理员' : '管理员'}
                </div>
              </div>
            </div>
          </Dropdown>
        </Header>

        {/* 主内容区域 */}
        <Content className="bg-gray-50 p-6 overflow-auto">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
